// This is a web-only implementation
// Platform detection - always web for this build
const Platform = {
  OS: 'web'
};

export interface ShareOptions {
  title?: string;
  message?: string;
  url?: string;
  filename?: string;
  type?: string;
  base64?: boolean;
}

export interface ShareFileOptions {
  url: string;
  filename: string;
  title?: string;
  message?: string;
  type?: string;
}

/**
 * Share text content across platforms
 */
export const shareText = async (options: ShareOptions): Promise<boolean> => {
  console.log('shareText called with:', options);
  console.log('Platform:', Platform.OS);

  try {
    if (Platform.OS === 'web') {
      console.log('Web platform detected');

      // Check if Web Share API is available and supported
      if (navigator.share) {
        console.log('navigator.share available');

        try {
          // Create share data with required fields only
          const shareData: any = {
            title: options.title || 'PlomDesign Mobile',
            text: options.message || 'Shared from PlomDesign Mobile'
          };

          // Only add URL if it's a valid URL
          if (options.url && (options.url.startsWith('http') || options.url.startsWith('https'))) {
            shareData.url = options.url;
          }

          console.log('Share data prepared:', shareData);

          // Check if we can share this data
          if (navigator.canShare && !navigator.canShare(shareData)) {
            console.log('navigator.canShare returned false, trying without URL');
            // Try without URL if canShare fails
            delete shareData.url;
            if (navigator.canShare && !navigator.canShare(shareData)) {
              throw new Error('Cannot share this data');
            }
          }

          console.log('Calling navigator.share...');
          // Use a small delay to ensure the user gesture is properly registered
          await new Promise(resolve => setTimeout(resolve, 10));
          await navigator.share(shareData);
          console.log('Share completed successfully');
          return true;

        } catch (shareError: any) {
          console.log('Web Share API failed:', shareError.name, shareError.message);

          // If user cancelled, don't treat as error
          if (shareError.name === 'AbortError') {
            console.log('User cancelled share');
            return false;
          }

          // Fall through to clipboard fallback for other errors
        }
      } else {
        console.log('navigator.share not available');
      }

      // Fallback for web - copy to clipboard
      if (navigator.clipboard && options.message) {
        console.log('Falling back to clipboard');
        try {
          await navigator.clipboard.writeText(options.message);
          alert('Content copied to clipboard!');
          return true;
        } catch (clipboardError) {
          console.log('Clipboard failed:', clipboardError);
        }
      }

      // Final fallback - show alert
      console.log('Using alert fallback');
      alert(options.message || 'Share content');
      return true; // Consider this successful since we showed the content
    }
  } catch (error) {
    console.error('Error sharing text:', error);
    return false;
  }
};

/**
 * Share a file (PDF, image, etc.) across platforms
 */
export const shareFile = async (options: ShareFileOptions): Promise<boolean> => {
  console.log('shareFile called with:', options);
  console.log('Platform:', Platform.OS);

  try {
    if (Platform.OS === 'web') {
      console.log('Web platform file sharing');

      // Check if Web Share API supports files
      if (navigator.share) {
        console.log('Attempting web file sharing');
        try {
          // Fetch the blob from the URL
          const response = await fetch(options.url);
          const blob = await response.blob();
          console.log('Blob created:', blob.type, blob.size);

          const file = new File([blob], options.filename, {
            type: options.type || blob.type || 'application/pdf'
          });
          console.log('File created:', file.name, file.type, file.size);

          const shareData: any = {
            files: [file]
          };

          // Add title if provided (but not text with files as it can cause issues)
          if (options.title) {
            shareData.title = options.title;
          }

          console.log('File share data:', shareData);

          // Check if we can share files
          if (navigator.canShare && navigator.canShare(shareData)) {
            console.log('Can share files, attempting share');
            // Small delay to ensure proper user gesture handling
            await new Promise(resolve => setTimeout(resolve, 10));
            await navigator.share(shareData);
            console.log('File share successful');
            return true;
          } else {
            console.log('Cannot share files - falling back to download + text share');
          }
        } catch (error: any) {
          console.warn('Web file sharing failed:', error);

          // If user cancelled, don't treat as error
          if (error.name === 'AbortError') {
            console.log('User cancelled file share');
            return false;
          }
        }
      }

      // Fallback for web - trigger download and show share text
      console.log('Falling back to download');
      const link = document.createElement('a');
      link.href = options.url;
      link.download = options.filename;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Also try to share the message as text
      if (options.message) {
        console.log('Also sharing message as text');
        return await shareText({
          title: options.title,
          message: `${options.message}\n\nFile downloaded: ${options.filename}`,
        });
      }

      return true;
    }
  } catch (error) {
    console.error('Error sharing file:', error);
    return false;
  }
};

/**
 * Check if native sharing is available on the current platform
 */
export const isNativeSharingAvailable = (): boolean => {
  return false; // Native sharing has been disabled
};

/**
 * Share via specific platform/method
 */
export const shareViaMethod = async (method: string, shareData: {
  title: string;
  text: string;
  url?: string;
  filename?: string;
}): Promise<boolean> => {
  console.log(`Sharing via ${method}:`, shareData);

  try {
    switch (method) {


      case 'email':
        const emailSubject = encodeURIComponent(shareData.title);
        const emailBody = encodeURIComponent(shareData.text + (shareData.url ? `\n\n${shareData.url}` : ''));
        window.open(`mailto:?subject=${emailSubject}&body=${emailBody}`, '_blank');
        return true;

      case 'whatsapp':
        const whatsappText = encodeURIComponent(shareData.text + (shareData.url ? `\n${shareData.url}` : ''));
        window.open(`https://wa.me/?text=${whatsappText}`, '_blank');
        return true;

      case 'messenger':
        const messengerText = encodeURIComponent(shareData.text);
        const messengerUrl = shareData.url ? encodeURIComponent(shareData.url) : '';

        // Use Facebook Messenger's web interface
        if (messengerUrl) {
          // Share with URL
          window.open(`https://www.facebook.com/dialog/send?link=${messengerUrl}&app_id=140586622674265&redirect_uri=${encodeURIComponent(window.location.href)}`, '_blank');
        } else {
          // Share text only - use Facebook share dialog
          const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}&quote=${messengerText}`;
          window.open(shareUrl, '_blank');
        }
        return true;

      case 'telegram':
        const telegramText = encodeURIComponent(shareData.text);
        const telegramUrl = shareData.url ? `&url=${encodeURIComponent(shareData.url)}` : '';
        window.open(`https://t.me/share/url?text=${telegramText}${telegramUrl}`, '_blank');
        return true;

      case 'twitter':
        const twitterText = encodeURIComponent(shareData.text.substring(0, 240)); // Twitter character limit
        const twitterUrl = shareData.url ? `&url=${encodeURIComponent(shareData.url)}` : '';
        window.open(`https://twitter.com/intent/tweet?text=${twitterText}${twitterUrl}`, '_blank');
        return true;

      case 'facebook':
        if (shareData.url) {
          window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareData.url)}`, '_blank');
        } else {
          window.open(`https://www.facebook.com/sharer/sharer.php?quote=${encodeURIComponent(shareData.text)}`, '_blank');
        }
        return true;

      case 'linkedin':
        const linkedinUrl = shareData.url ? encodeURIComponent(shareData.url) : '';
        const linkedinTitle = encodeURIComponent(shareData.title);
        const linkedinSummary = encodeURIComponent(shareData.text.substring(0, 200));
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${linkedinUrl}&title=${linkedinTitle}&summary=${linkedinSummary}`, '_blank');
        return true;

      case 'teams':
        const teamsText = encodeURIComponent(shareData.text);
        const teamsUrl = shareData.url ? encodeURIComponent(shareData.url) : '';
        const teamsTitle = encodeURIComponent(shareData.title);

        // Microsoft Teams web sharing URL
        if (shareData.url) {
          window.open(`https://teams.microsoft.com/share?href=${teamsUrl}&msgText=${teamsText}`, '_blank');
        } else {
          // For text-only sharing, use the Teams compose message URL
          window.open(`https://teams.microsoft.com/l/compose?subject=${teamsTitle}&body=${teamsText}`, '_blank');
        }
        return true;

      case 'download':
        if (shareData.url && shareData.filename) {
          const link = document.createElement('a');
          link.href = shareData.url;
          link.download = shareData.filename;
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          return true;
        }
        return false;

      default:
        console.warn(`Unknown share method: ${method}`);
        return false;
    }
  } catch (error) {
    console.error(`Error sharing via ${method}:`, error);
    return false;
  }
};

/**
 * Debug Web Share API capabilities
 */
export const debugWebShareAPI = () => {
  const debug = {
    navigatorShareExists: !!(navigator.share),
    navigatorCanShareExists: !!(navigator.canShare),
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    isSecureContext: window.isSecureContext,
    protocol: window.location.protocol,
    hostname: window.location.hostname,
    userActivation: navigator.userActivation ? {
      hasBeenActive: navigator.userActivation.hasBeenActive,
      isActive: navigator.userActivation.isActive
    } : 'not available'
  };

  console.log('Web Share API Debug Info:', debug);
  return debug;
};



/**
 * Get platform-specific sharing capabilities
 */
export const getSharingCapabilities = () => {
  return {
    platform: Platform.OS,
    nativeShareAvailable: isNativeSharingAvailable(),
    canShareFiles: Platform.OS !== 'web' || !!(navigator.share && navigator.canShare),
    canShareText: true,
    fallbackMethod: Platform.OS === 'web' ? 'download/clipboard' : 'none',
  };
};
