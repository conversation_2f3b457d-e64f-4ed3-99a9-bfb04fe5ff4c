import React, { useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { debugWebShareAPI } from '../utils/shareUtils';
import SocialIcon, { SocialIconType } from './SocialIcon';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  shareData: {
    title: string;
    text: string;
    url?: string;
    filename?: string;
  };
  onShareOption: (option: string) => void;
}

const ShareModal: React.FC<ShareModalProps> = ({ isOpen, onClose, shareData, onShareOption }) => {
  const { isDark } = useTheme();

  // Debug Web Share API when modal opens
  useEffect(() => {
    if (isOpen) {
      debugWebShareAPI();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  // Detect Windows platform for Teams integration
  const isWindows = typeof navigator !== 'undefined' &&
    (navigator.platform.toLowerCase().includes('win') ||
     navigator.userAgent.toLowerCase().includes('windows'));

  const shareOptions = [
    {
      id: 'email' as SocialIconType,
      name: 'Email',
      description: 'Share via email',
      available: true
    },
    {
      id: 'whatsapp' as SocialIconType,
      name: 'WhatsApp',
      description: 'Share to WhatsApp',
      available: true
    },
    {
      id: 'messenger' as SocialIconType,
      name: 'Messenger',
      description: 'Share to Facebook Messenger',
      available: true
    },
    {
      id: 'telegram' as SocialIconType,
      name: 'Telegram',
      description: 'Share to Telegram',
      available: true
    },
    {
      id: 'twitter' as SocialIconType,
      name: 'Twitter/X',
      description: 'Share to Twitter',
      available: true
    },
    {
      id: 'facebook' as SocialIconType,
      name: 'Facebook',
      description: 'Share to Facebook',
      available: true
    },
    {
      id: 'linkedin' as SocialIconType,
      name: 'LinkedIn',
      description: 'Share to LinkedIn',
      available: true
    },
    {
      id: 'teams' as SocialIconType,
      name: 'Microsoft Teams',
      description: 'Share to Microsoft Teams',
      available: isWindows
    },
    {
      id: 'download' as SocialIconType,
      name: 'Download',
      description: 'Download file',
      available: !!shareData.url
    }
  ];

  const availableOptions = shareOptions.filter(option => option.available);

  const handleOptionClick = async (optionId: string) => {
    console.log('ShareModal handleOptionClick called with:', optionId);

    // For all options, use the parent handler
    console.log('Using parent handler for option:', optionId);
    onShareOption(optionId);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
        padding: '20px'
      }}
      onClick={handleBackdropClick}
    >
      <div
        style={{
          backgroundColor: isDark ? '#1f2937' : '#ffffff',
          borderRadius: '16px',
          padding: '24px',
          maxWidth: '400px',
          width: '100%',
          maxHeight: '80vh',
          overflowY: 'auto',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div style={{ marginBottom: '20px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <h3 style={{
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              color: isDark ? '#f9fafb' : '#111827'
            }}>
              Share Invoice
            </h3>
            <button
              onClick={onClose}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: isDark ? '#9ca3af' : '#6b7280',
                padding: '4px',
                borderRadius: '4px'
              }}
            >
              ×
            </button>
          </div>
          <p style={{
            margin: 0,
            fontSize: '14px',
            color: isDark ? '#9ca3af' : '#6b7280'
          }}>
            {shareData.title}
          </p>
        </div>



        {/* Share Options Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
          gap: '12px',
          marginBottom: '20px'
        }}>
          {availableOptions.map((option) => (
            <button
              key={option.id}
              onClick={() => handleOptionClick(option.id)}
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                padding: '16px 12px',
                border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
                borderRadius: '12px',
                backgroundColor: isDark ? '#374151' : '#f9fafb',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                textAlign: 'center'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = isDark ? '#4b5563' : '#f3f4f6';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = isDark ? '#374151' : '#f9fafb';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <div style={{ marginBottom: '8px', display: 'flex', justifyContent: 'center' }}>
                <SocialIcon type={option.id} size={24} />
              </div>
              <div style={{
                fontSize: '12px',
                fontWeight: '500',
                color: isDark ? '#f9fafb' : '#111827',
                marginBottom: '4px'
              }}>
                {option.name}
              </div>
              <div style={{
                fontSize: '10px',
                color: isDark ? '#9ca3af' : '#6b7280',
                lineHeight: '1.2'
              }}>
                {option.description}
              </div>
            </button>
          ))}
        </div>

        {/* Cancel Button */}
        <button
          onClick={onClose}
          style={{
            width: '100%',
            padding: '12px',
            border: `1px solid ${isDark ? '#4b5563' : '#d1d5db'}`,
            borderRadius: '8px',
            backgroundColor: 'transparent',
            color: isDark ? '#f9fafb' : '#374151',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default ShareModal;
