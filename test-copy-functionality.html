<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Copy Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Copy Functionality Test</h1>
        <p>This page tests the copy functionality for invoice PDF sharing.</p>

        <div class="test-section">
            <h3>Test 1: Copy with Blob URL (Current Issue)</h3>
            <p>This simulates the current problematic behavior with blob URLs.</p>
            <button onclick="testCopyWithBlobUrl()">Test Copy with Blob URL</button>
            <div id="result1" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: Copy with Fixed Implementation</h3>
            <p>This tests the improved copy functionality that provides meaningful content.</p>
            <button onclick="testCopyWithFixedImplementation()">Test Fixed Copy</button>
            <div id="result2" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Copy with HTTP URL</h3>
            <p>This tests copying with a valid HTTP URL.</p>
            <button onclick="testCopyWithHttpUrl()">Test Copy with HTTP URL</button>
            <div id="result3" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Test 4: Clipboard Fallback</h3>
            <p>This tests the fallback mechanism when clipboard API fails.</p>
            <button onclick="testClipboardFallback()">Test Clipboard Fallback</button>
            <div id="result4" class="result"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        async function testCopyWithBlobUrl() {
            showResult('result1', 'Testing copy with blob URL...', 'info');
            
            try {
                // Simulate the old problematic behavior
                const shareData = {
                    text: "📄 PlomDesign Invoice - Test Canvas\n\nProducts:\n1. Test Product (Qty: 1 - 1 instances)\n   Category: Test / Test\n   Price: $10.00\n   Material: Test\n\nTotal Amount: $10.00\n\n✅ PDF Generated Successfully\nGenerated by PlomDesign Mobile",
                    url: "blob:http://localhost:3002/12345678-1234-1234-1234-123456789abc"
                };

                const textToCopy = shareData.text + '\n' + shareData.url;
                await navigator.clipboard.writeText(textToCopy);
                
                showResult('result1', 'PROBLEMATIC: Content copied with blob URL:\n\n' + textToCopy + '\n\n❌ The blob URL is not useful for sharing!', 'error');
            } catch (error) {
                showResult('result1', 'Error: ' + error.message, 'error');
            }
        }

        async function testCopyWithFixedImplementation() {
            showResult('result2', 'Testing fixed copy implementation...', 'info');
            
            try {
                const shareData = {
                    text: "📄 PlomDesign Invoice - Test Canvas\n\nProducts:\n1. Test Product (Qty: 1 - 1 instances)\n   Category: Test / Test\n   Price: $10.00\n   Material: Test\n\nTotal Amount: $10.00\n\n✅ PDF Generated Successfully\nGenerated by PlomDesign Mobile",
                    url: "blob:http://localhost:3002/12345678-1234-1234-1234-123456789abc"
                };

                // Use the fixed implementation logic
                let textToCopy = shareData.text;
                
                if (shareData.url && shareData.url.startsWith('blob:')) {
                    textToCopy += '\n\nNote: PDF file was generated locally. To share the actual file, please use the Download option or native sharing.';
                }

                await navigator.clipboard.writeText(textToCopy);
                
                showResult('result2', '✅ FIXED: Content copied with helpful message:\n\n' + textToCopy, 'success');
            } catch (error) {
                showResult('result2', 'Error: ' + error.message, 'error');
            }
        }

        async function testCopyWithHttpUrl() {
            showResult('result3', 'Testing copy with HTTP URL...', 'info');
            
            try {
                const shareData = {
                    text: "📄 PlomDesign Invoice - Test Canvas\n\nProducts:\n1. Test Product (Qty: 1 - 1 instances)\n   Category: Test / Test\n   Price: $10.00\n   Material: Test\n\nTotal Amount: $10.00\n\nGenerated by PlomDesign Mobile",
                    url: "https://example.com/invoice.pdf"
                };

                let textToCopy = shareData.text;
                
                if (shareData.url && (shareData.url.startsWith('http://') || shareData.url.startsWith('https://'))) {
                    textToCopy += `\n\nLink: ${shareData.url}`;
                }

                await navigator.clipboard.writeText(textToCopy);
                
                showResult('result3', '✅ SUCCESS: Content copied with valid URL:\n\n' + textToCopy, 'success');
            } catch (error) {
                showResult('result3', 'Error: ' + error.message, 'error');
            }
        }

        async function testClipboardFallback() {
            showResult('result4', 'Testing clipboard fallback...', 'info');
            
            try {
                const shareData = {
                    text: "📄 PlomDesign Invoice - Test Canvas\n\nProducts:\n1. Test Product (Qty: 1 - 1 instances)\n   Category: Test / Test\n   Price: $10.00\n   Material: Test\n\nTotal Amount: $10.00\n\nGenerated by PlomDesign Mobile"
                };

                // Simulate the fallback method
                const textArea = document.createElement('textarea');
                textArea.value = shareData.text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (successful) {
                    showResult('result4', '✅ FALLBACK SUCCESS: Content copied using execCommand:\n\n' + shareData.text, 'success');
                } else {
                    showResult('result4', '❌ FALLBACK FAILED: execCommand did not work', 'error');
                }
            } catch (error) {
                showResult('result4', 'Error: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
